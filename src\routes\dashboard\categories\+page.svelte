<script lang="ts">
  import { goto } from '$app/navigation';
  import type { PageData } from './$types';
  import { slide } from 'svelte/transition';

  export let data: PageData;

  let categories = data.categories;
  let showAddForm = false;
  let newCategoryName = '';
  let newCategoryColor = '#3b82f6';
  let loading = false;
  let error = '';
  let success = '';

  const predefinedColors = [
    '#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ef4444',
    '#06b6d4', '#ec4899', '#84cc16', '#f97316', '#6366f1'
  ];

  async function addCategory() {
    if (!newCategoryName.trim()) {
      error = 'Category name is required';
      return;
    }

    loading = true;
    error = '';

    try {
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newCategoryName.trim(),
          color: newCategoryColor
        }),
      });

      if (response.ok) {
        const { category } = await response.json();
        categories = [...categories, category];
        newCategoryName = '';
        newCategoryColor = '#3b82f6';
        showAddForm = false;
        success = 'Category added successfully!';
        setTimeout(() => success = '', 3000);
      } else {
        const { error: errorMsg } = await response.json();
        error = errorMsg || 'Failed to add category';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }

  async function deleteCategory(categoryId: string) {
    if (!confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      return;
    }

    loading = true;
    error = '';

    try {
      const response = await fetch(`/api/categories/${categoryId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        categories = categories.filter(c => c.id !== categoryId);
        success = 'Category deleted successfully!';
        setTimeout(() => success = '', 3000);
      } else {
        const { error: errorMsg } = await response.json();
        error = errorMsg || 'Failed to delete category';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }
</script>

<div class="categories-container">
  <div class="categories-header">
    <h1>Manage Categories</h1>
    <p>Organize your tasks with custom categories</p>
  </div>

  {#if error}
    <div class="error-message">{error}</div>
  {/if}

  {#if success}
    <div class="success-message">{success}</div>
  {/if}

  <div class="categories-content">
    <div class="add-category-section">
      <button
        type="button"
        class="add-category-btn"
        on:click={() => showAddForm = !showAddForm}
        disabled={loading}
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="12" y1="5" x2="12" y2="19"></line>
          <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
        Add New Category
      </button>

      {#if showAddForm}
        <div class="add-form" transition:slide>
          <div class="form-group">
            <label for="categoryName">Category Name</label>
            <input
              id="categoryName"
              type="text"
              bind:value={newCategoryName}
              placeholder="Enter category name"
              disabled={loading}
              maxlength="50"
            />
          </div>

          <div class="form-group">
            <label for="categoryColor">Color</label>
            <div class="color-picker">
              {#each predefinedColors as color}
                <button
                  type="button"
                  class="color-option"
                  class:selected={newCategoryColor === color}
                  style="background: {color};"
                  on:click={() => newCategoryColor = color}
                  disabled={loading}
                ></button>
              {/each}
            </div>
          </div>

          <div class="form-actions">
            <button
              type="button"
              class="btn-secondary"
              on:click={() => { showAddForm = false; newCategoryName = ''; newCategoryColor = '#3b82f6'; }}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="button"
              class="btn-primary"
              on:click={addCategory}
              disabled={loading || !newCategoryName.trim()}
            >
              {loading ? 'Adding...' : 'Add Category'}
            </button>
          </div>
        </div>
      {/if}
    </div>

    <div class="categories-list">
      {#if categories.length === 0}
        <div class="empty-state">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="9" y1="9" x2="15" y2="15"></line>
            <line x1="15" y1="9" x2="9" y2="15"></line>
          </svg>
          <h3>No categories yet</h3>
          <p>Create your first category to organize your tasks</p>
        </div>
      {:else}
        <div class="categories-grid">
          {#each categories as category}
            <div class="category-card">
              <div class="category-info">
                <div class="category-color" style="background: {category.color};"></div>
                <span class="category-name">{category.name}</span>
              </div>
              <button
                type="button"
                class="delete-btn"
                on:click={() => deleteCategory(category.id)}
                disabled={loading}
                title="Delete category"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="3,6 5,6 21,6"></polyline>
                  <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                </svg>
              </button>
            </div>
          {/each}
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  .categories-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
  }

  .categories-header {
    margin-bottom: 2rem;
  }

  .categories-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
  }

  .categories-header p {
    color: #6b7280;
    font-size: 1rem;
  }

  .error-message, .success-message {
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 500;
  }

  .error-message {
    background: linear-gradient(135deg, #fef2f2, #fed7d7);
    color: #dc2626;
    border: 1px solid #fecaca;
  }

  .success-message {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    color: #16a34a;
    border: 1px solid #bbf7d0;
  }

  .categories-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .add-category-section {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .add-category-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.25rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  }

  .add-category-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
  }

  .add-form {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-group label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .form-group input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }

  .form-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  }

  .color-picker {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .color-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .color-option:hover {
    transform: scale(1.1);
  }

  .color-option.selected {
    border-color: #1f2937;
    transform: scale(1.1);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
  }

  .btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
  }

  .btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  }

  .btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
  }

  .btn-secondary {
    background: white;
    color: #374151;
    border-color: #e5e7eb;
  }

  .btn-secondary:hover:not(:disabled) {
    border-color: #d1d5db;
    background: #f9fafb;
  }

  .categories-list {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
  }

  .empty-state svg {
    margin: 0 auto 1rem;
    opacity: 0.5;
  }

  .empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #374151;
  }

  .categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }

  .category-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    background: #f8fafc;
    transition: all 0.2s ease;
  }

  .category-card:hover {
    border-color: #d1d5db;
    background: #f1f5f9;
  }

  .category-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .category-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    flex-shrink: 0;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .category-name {
    font-weight: 600;
    color: #374151;
  }

  .delete-btn {
    padding: 0.5rem;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
  }

  .delete-btn:hover:not(:disabled) {
    color: #ef4444;
    background: #fef2f2;
  }

  @media (max-width: 768px) {
    .categories-container {
      padding: 1rem;
    }

    .categories-grid {
      grid-template-columns: 1fr;
    }

    .form-actions {
      flex-direction: column;
    }
  }
</style>
