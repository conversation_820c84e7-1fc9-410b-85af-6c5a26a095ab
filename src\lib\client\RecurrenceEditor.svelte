<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  import { slide } from 'svelte/transition';

  export let rule: any = null;

  const dispatch = createEventDispatcher();

  const DEFAULT_RULE = {
    type: 'daily', // daily, weekly, monthly, yearly
    interval: 1,
    weekdays: [] as number[], // 0=Sun, 1=Mon...
    monthlyType: 'date', // date, weekday, last_day, last_x_day
    monthlyDate: 1, // 1-31
    monthlyLastXDay: 1,
    monthlyWeekNumber: 1, // 1, 2, 3, 4, -1 (last)
    monthlyWeekday: 1, // 0=Sun, 1=Mon...
    yearlyMonth: 1, // 1-12
    endType: 'never', // never, on, after
    endOnDate: '',
    endAfterOccurrences: 10,
  };

  let internalRule = rule ? { ...DEFAULT_RULE, ...rule } : { ...DEFAULT_RULE };

  onMount(() => {
    if (!rule) {
      // Initialize with default rule when no rule is provided
      internalRule = { ...DEFAULT_RULE };
      updateRule();
    }
  });

  const weekDays = [
    { value: 1, label: 'Monday' }, { value: 2, label: 'Tuesday' }, { value: 3, label: 'Wednesday' },
    { value: 4, label: 'Thursday' }, { value: 5, label: 'Friday' }, { value: 6, label: 'Saturday' },
    { value: 0, label: 'Sunday' }
  ];
  const weekNumbers = [
    { value: 1, label: 'First' }, { value: 2, label: 'Second' }, { value: 3, label: 'Third' },
    { value: 4, label: 'Fourth' }, { value: -1, label: 'Last' }
  ];
  const months = [
      {value: 1, label: 'January'}, {value: 2, label: 'February'}, {value: 3, label: 'March'},
      {value: 4, label: 'April'}, {value: 5, label: 'May'}, {value: 6, label: 'June'},
      {value: 7, label: 'July'}, {value: 8, label: 'August'}, {value: 9, label: 'September'},
      {value: 10, label: 'October'}, {value: 11, label: 'November'}, {value: 12, label: 'December'}
  ];




  function toggleWeekday(day: number) {
    const weekdays = new Set(internalRule.weekdays);
    if (weekdays.has(day)) weekdays.delete(day);
    else weekdays.add(day);
    internalRule.weekdays = Array.from(weekdays).sort((a,b) => a-b);
    updateRule();
  }

  function updateRule() {
    // Basic validation
    if (internalRule.interval < 1) internalRule.interval = 1;
    if (internalRule.endAfterOccurrences < 1) internalRule.endAfterOccurrences = 1;
    if (internalRule.monthlyDate < 1) internalRule.monthlyDate = 1;
    if (internalRule.monthlyDate > 31) internalRule.monthlyDate = 31;
    if (internalRule.monthlyLastXDay < 1) internalRule.monthlyLastXDay = 1;
    if (internalRule.monthlyLastXDay > 31) internalRule.monthlyLastXDay = 31;
    dispatch('change', { ...internalRule });
  }

  $: summary = generateSummary(internalRule);

  function generateSummary(r: typeof internalRule | null): string {
    if (!r) return 'No recurrence set.';

    let summary = 'Repeats ';
    if (r.interval > 1) {
        summary += `every ${r.interval} ${r.type}s`;
    } else {
        summary += r.type;
    }

    if (r.type === 'weekly' && r.weekdays.length > 0) {
        const sortedDays = [...r.weekdays].sort();
        const dayNames = sortedDays.map(d => weekDays.find(wd => wd.value === d)?.label).join(', ');
        summary += ` on ${dayNames}`;
    }

    if (r.type === 'monthly') {
        switch(r.monthlyType) {
            case 'date':
                summary += ` on day ${r.monthlyDate}`;
                break;
            case 'weekday':
                const weekNum = weekNumbers.find(wn => wn.value === r.monthlyWeekNumber)?.label.toLowerCase();
                const weekDay = weekDays.find(wd => wd.value === r.monthlyWeekday)?.label;
                summary += ` on the ${weekNum} ${weekDay}`;
                break;
            case 'last_day':
                summary += ' on the last day';
                break;
            case 'last_x_day':
                summary += ` on the ${r.monthlyLastXDay === 1 ? 'last' : `${r.monthlyLastXDay}th to last`} day`;
                break;
        }
    }

    if (r.type === 'yearly') {
        const monthName = months.find(m => m.value === r.yearlyMonth)?.label;
        summary += ` on ${monthName} ${r.monthlyDate}`;
    }
    
    if (r.endType === 'on' && r.endOnDate) summary += `, until ${r.endOnDate}`;
    if (r.endType === 'after') summary += `, for ${r.endAfterOccurrences} occurrences`;

    return summary + '.';
  }

</script>

<div class="recurrence-editor">
  <div class="recurrence-settings" transition:slide>
      <div class="setting-row">
        <label for="interval">Repeat every</label>
        <input type="number" id="interval" min="1" class="interval-input" bind:value={internalRule.interval} on:change={updateRule}>
        <select class="unit-select" bind:value={internalRule.type} on:change={updateRule}>
          <option value="daily">day(s)</option>
          <option value="weekly">week(s)</option>
          <option value="monthly">month(s)</option>
          <option value="yearly">year(s)</option>
        </select>
      </div>

      {#if internalRule.type === 'weekly'}
        <div class="setting-row weekdays">
          <label>On</label>
          <div class="weekday-selector">
            {#each weekDays as day}
              <button type="button" class="weekday-btn" class:active={internalRule.weekdays.includes(day.value)} on:click={() => toggleWeekday(day.value)}>{day.label}</button>
            {/each}
          </div>
        </div>
      {/if}

      {#if internalRule.type === 'monthly'}
        <div class="monthly-options">
            <div class="radio-group">
              <input type="radio" id="monthly-date" value="date" bind:group={internalRule.monthlyType} on:change={updateRule}>
              <label for="monthly-date">On day</label>
              <input type="number" min="1" max="31" class="day-input" bind:value={internalRule.monthlyDate} disabled={internalRule.monthlyType !== 'date'} on:change={updateRule}>
            </div>
            <div class="radio-group">
              <input type="radio" id="monthly-weekday" value="weekday" bind:group={internalRule.monthlyType} on:change={updateRule}>
              <label for="monthly-weekday">On the</label>
              <select bind:value={internalRule.monthlyWeekNumber} disabled={internalRule.monthlyType !== 'weekday'} on:change={updateRule}>
                  {#each weekNumbers as wn}<option value={wn.value}>{wn.label}</option>{/each}
              </select>
              <select bind:value={internalRule.monthlyWeekday} disabled={internalRule.monthlyType !== 'weekday'} on:change={updateRule}>
                  {#each weekDays as wd}<option value={wd.value}>{wd.label}</option>{/each}
              </select>
            </div>
             <div class="radio-group">
              <input type="radio" id="monthly-lastday" value="last_day" bind:group={internalRule.monthlyType} on:change={updateRule}>
              <label for="monthly-lastday">On the last day of the month</label>
            </div>
             <div class="radio-group">
              <input type="radio" id="monthly-last-x-day" value="last_x_day" bind:group={internalRule.monthlyType} on:change={updateRule}>
               <input type="number" min="1" max="31" class="day-input" bind:value={internalRule.monthlyLastXDay} disabled={internalRule.monthlyType !== 'last_x_day'} on:change={updateRule}>
              <label for="monthly-last-x-day">day(s) before the end of the month</label>
            </div>
        </div>
      {/if}
      
      {#if internalRule.type === 'yearly'}
        <div class="setting-row">
            <label>On</label>
             <select bind:value={internalRule.yearlyMonth} on:change={updateRule}>
                {#each months as month}<option value={month.value}>{month.label}</option>{/each}
            </select>
            <input type="number" min="1" max="31" class="day-input" bind:value={internalRule.monthlyDate} on:change={updateRule}>
        </div>
      {/if}

      <div class="setting-row end-condition">
          <label>Ends</label>
          <div class="end-options">
               <div class="radio-group">
                  <input type="radio" id="end-never" value="never" bind:group={internalRule.endType} on:change={updateRule}>
                  <label for="end-never">Never</label>
              </div>
               <div class="radio-group">
                  <input type="radio" id="end-on" value="on" bind:group={internalRule.endType} on:change={updateRule}>
                  <label for="end-on">On</label>
                  <input type="date" bind:value={internalRule.endOnDate} disabled={internalRule.endType !== 'on'} on:change={updateRule}>
              </div>
               <div class="radio-group">
                  <input type="radio" id="end-after" value="after" bind:group={internalRule.endType} on:change={updateRule}>
                  <label for="end-after">After</label>
                  <input type="number" min="1" class="occurrences-input" bind:value={internalRule.endAfterOccurrences} disabled={internalRule.endType !== 'after'} on:change={updateRule}>
                  <span>occurrences</span>
              </div>
          </div>
      </div>
  </div>

  {#if internalRule}
    <div class="summary">
      <strong>Summary:</strong> {summary}
    </div>
  {/if}
</div>

<style>
  .recurrence-editor {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 1.5rem;
    font-size: 0.875rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  }

  .recurrence-settings {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  .setting-row, .monthly-options {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.8);
    padding: 1.25rem;
    border-radius: 16px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.2s ease;
  }

  .setting-row:hover, .monthly-options:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border-color: #d1d5db;
  }

  .monthly-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.25rem;
  }

  .setting-row label {
    font-weight: 600;
    color: #374151;
    margin-right: 0.75rem;
    font-size: 0.875rem;
    min-width: fit-content;
  }
  input[type="number"], select, input[type="date"] {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    appearance: none;
    cursor: pointer;
  }

  select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 3rem;
  }

  input[type="number"]:focus, select:focus, input[type="date"]:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
  }

  select:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%233b82f6' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }

  .interval-input, .day-input, .occurrences-input {
    width: 90px;
  }
  .weekday-selector {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .weekday-btn {
    min-width: 50px;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    background: white;
    cursor: pointer;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
    color: #374151;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    font-size: 0.8rem;
  }

  .weekday-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  }

  .weekday-btn.active {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  }
  .radio-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1rem;
    background: rgba(248, 250, 252, 0.7);
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    width: 100%;
  }

  .radio-group:hover {
    background: rgba(248, 250, 252, 0.9);
    border-color: #d1d5db;
  }

  .radio-group label {
    font-weight: 500;
    color: #374151;
    cursor: pointer;
  }

  .radio-group input[type="radio"] {
    margin: 0;
    transform: scale(1.1);
  }

  .summary {
    margin-top: 1.5rem;
    padding: 1.25rem 1.5rem;
    color: #374151;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(29, 78, 216, 0.06));
    border-radius: 16px;
    border: 1px solid rgba(59, 130, 246, 0.15);
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  }

  .summary strong {
    color: #1f2937;
    font-weight: 600;
  }
</style>