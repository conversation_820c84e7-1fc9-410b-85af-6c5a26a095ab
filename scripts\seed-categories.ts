import { db } from '../src/lib/server/db/index.js';
import { categories, users } from '../src/lib/server/db/schema.js';
import { eq } from 'drizzle-orm';

const defaultCategories = [
  { name: 'Work', color: '#3b82f6' },
  { name: 'Personal', color: '#10b981' },
  { name: 'Health', color: '#f59e0b' },
  { name: 'Learning', color: '#8b5cf6' },
  { name: 'Finance', color: '#ef4444' },
  { name: 'Home', color: '#06b6d4' },
  { name: 'Shopping', color: '#ec4899' },
  { name: 'Travel', color: '#84cc16' },
  { name: 'Family', color: '#f97316' },
  { name: 'Hobbies', color: '#6366f1' }
];

async function seedCategories() {
  try {
    console.log('Starting to seed categories...');
    
    // Get all users
    const allUsers = await db.select().from(users);
    console.log(`Found ${allUsers.length} users`);
    
    for (const user of allUsers) {
      console.log(`Seeding categories for user: ${user.email}`);
      
      // Check if user already has categories
      const existingCategories = await db.select().from(categories).where(eq(categories.userId, user.id));
      
      if (existingCategories.length === 0) {
        // Add default categories for this user
        for (const category of defaultCategories) {
          await db.insert(categories).values({
            userId: user.id,
            name: category.name,
            color: category.color
          });
        }
        console.log(`Added ${defaultCategories.length} categories for ${user.email}`);
      } else {
        console.log(`User ${user.email} already has ${existingCategories.length} categories, skipping...`);
      }
    }
    
    console.log('Categories seeding completed!');
  } catch (error) {
    console.error('Error seeding categories:', error);
  } finally {
    process.exit(0);
  }
}

seedCategories();
