<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { slide } from 'svelte/transition';
  import { clickOutside } from '$lib/client/clickOutside.js';

  export let value: string = '';
  export let options: Array<{value: string, label: string}> = [];
  export let placeholder: string = 'Select an option';
  export let disabled: boolean = false;
  export let size: 'small' | 'medium' | 'large' = 'medium';

  const dispatch = createEventDispatcher();
  
  let isOpen = false;
  let selectedOption = options.find(opt => opt.value === value);

  $: selectedOption = options.find(opt => opt.value === value);

  function toggleDropdown() {
    if (!disabled) {
      isOpen = !isOpen;
    }
  }

  function selectOption(option: {value: string, label: string}) {
    value = option.value;
    selectedOption = option;
    isOpen = false;
    dispatch('change', { value: option.value });
  }

  function handleKeydown(event: KeyboardEvent) {
    if (disabled) return;
    
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleDropdown();
    } else if (event.key === 'Escape') {
      isOpen = false;
    } else if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
      event.preventDefault();
      if (!isOpen) {
        isOpen = true;
        return;
      }
      
      const currentIndex = options.findIndex(opt => opt.value === value);
      let newIndex;
      
      if (event.key === 'ArrowDown') {
        newIndex = currentIndex < options.length - 1 ? currentIndex + 1 : 0;
      } else {
        newIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
      }
      
      selectOption(options[newIndex]);
    }
  }

  function handleClickOutside() {
    isOpen = false;
  }
</script>

<div 
  class="custom-select {size}" 
  class:disabled
  use:clickOutside={handleClickOutside}
>
  <button
    type="button"
    class="select-trigger"
    class:open={isOpen}
    on:click={toggleDropdown}
    on:keydown={handleKeydown}
    {disabled}
    aria-haspopup="listbox"
    aria-expanded={isOpen}
  >
    <span class="selected-text">
      {selectedOption?.label || placeholder}
    </span>
    <svg 
      class="dropdown-arrow" 
      class:rotated={isOpen}
      width="20" 
      height="20" 
      viewBox="0 0 20 20" 
      fill="none"
    >
      <path 
        stroke="currentColor" 
        stroke-linecap="round" 
        stroke-linejoin="round" 
        stroke-width="1.5" 
        d="m6 8 4 4 4-4"
      />
    </svg>
  </button>

  {#if isOpen}
    <div 
      class="dropdown-menu" 
      transition:slide={{ duration: 200 }}
      role="listbox"
    >
      {#each options as option}
        <button
          type="button"
          class="dropdown-option"
          class:selected={option.value === value}
          on:click={() => selectOption(option)}
          role="option"
          aria-selected={option.value === value}
        >
          {option.label}
        </button>
      {/each}
    </div>
  {/if}
</div>

<style>
  .custom-select {
    position: relative;
    display: inline-block;
  }

  .select-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    color: #374151;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    font-size: 0.875rem;
  }

  .custom-select.small .select-trigger {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .custom-select.large .select-trigger {
    padding: 1rem 1.25rem;
    font-size: 1rem;
  }

  .select-trigger:hover:not(:disabled) {
    border-color: #d1d5db;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }

  .select-trigger:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  }

  .select-trigger.open {
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  }

  .select-trigger:disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
    border-color: #e5e7eb;
    transform: none;
  }

  .custom-select.disabled {
    opacity: 0.6;
  }

  .selected-text {
    flex: 1;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .dropdown-arrow {
    margin-left: 0.5rem;
    transition: transform 0.2s ease;
    color: #6b7280;
    flex-shrink: 0;
  }

  .dropdown-arrow.rotated {
    transform: rotate(180deg);
  }

  .select-trigger:focus .dropdown-arrow,
  .select-trigger.open .dropdown-arrow {
    color: #3b82f6;
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 50;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    margin-top: 0.5rem;
    max-height: 300px;
    overflow-y: auto;
    backdrop-filter: blur(8px);
  }

  .dropdown-option {
    display: block;
    width: 100%;
    padding: 0.875rem 1rem;
    border: none;
    background: white;
    cursor: pointer;
    transition: all 0.15s ease;
    text-align: left;
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
  }

  .custom-select.small .dropdown-option {
    padding: 0.625rem 0.75rem;
    font-size: 0.8rem;
  }

  .custom-select.large .dropdown-option {
    padding: 1rem 1.25rem;
    font-size: 1rem;
  }

  .dropdown-option:first-child {
    border-radius: 12px 12px 0 0;
  }

  .dropdown-option:last-child {
    border-radius: 0 0 12px 12px;
  }

  .dropdown-option:only-child {
    border-radius: 12px;
  }

  .dropdown-option:hover:not(:disabled) {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #1e293b;
    transform: translateX(2px);
  }

  .dropdown-option.selected {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1d4ed8;
    font-weight: 600;
  }

  .dropdown-option.selected:hover {
    background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
    transform: translateX(2px);
  }

  /* Scrollbar styling for dropdown */
  .dropdown-menu::-webkit-scrollbar {
    width: 6px;
  }

  .dropdown-menu::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 6px;
  }

  .dropdown-menu::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 6px;
  }

  .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
</style>
