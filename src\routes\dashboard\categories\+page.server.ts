import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { getCategoriesByUserId } from '$lib/server/db/operations.js';

export const load: PageServerLoad = async ({ locals }) => {
  if (!locals.user) {
    throw redirect(302, '/login');
  }

  try {
    const categories = await getCategoriesByUserId(locals.user.id);

    return {
      categories
    };
  } catch (error) {
    console.error('Load categories page error:', error);
    return {
      categories: []
    };
  }
};
